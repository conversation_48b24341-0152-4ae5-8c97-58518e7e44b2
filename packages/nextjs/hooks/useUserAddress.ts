import { useEffect } from "react";
import { useMagicStore } from "@/services/store/magicStore";
import { useAccount } from "wagmi";

interface UserInfo {
  address?: string;
  displayName?: string;
  ensAvatar?: string;
  email?: string;
  isWagmi: boolean;
  isMagic: boolean;
  isConnected: boolean;
}

export const useUserAddress = (): UserInfo => {
  const { address: wagmiAddress, isConnected: wagmiConnected } = useAccount();
  const { magicUser, isLoggedIn: isMagicLoggedIn, initializeMagic, isInitialized } = useMagicStore();
  useEffect(() => {
    if (!isInitialized) {
      initializeMagic();
    }
  }, [isInitialized, initializeMagic]);

  const address = magicUser?.publicAddress || wagmiAddress;
  const isConnected = isMagicLoggedIn || wagmiConnected;
  const isWagmi = Boolean(wagmiAddress && !magicUser);
  const isMagic = isMagicLoggedIn;

  return {
    address,
    displayName: magicUser?.email,
    ensAvatar: undefined,
    email: magicUser?.email,
    isWagmi,
    isMagic,
    isConnected,
  };
};
