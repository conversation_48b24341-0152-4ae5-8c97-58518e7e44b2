import { useCallback, useEffect, useState } from "react";
import { PointsAndFeesData, getPointsAndFees } from "@/api/user/pointsAndFees";
import { WalletType, useGlobalState } from "@/services/store/store";
import { getItem } from "@/utils";

interface UsePointsAndFeesReturn {
  data: PointsAndFeesData | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * Hook for fetching user points and fees data (L2Header型接口)
 * @param user_wallet 用户钱包地址
 * @param enabled 是否启用自动获取数据
 * @returns UsePointsAndFeesReturn
 */
export const usePointsAndFees = (user_wallet: string | null, enabled = true): UsePointsAndFeesReturn => {
  const [data, setData] = useState<PointsAndFeesData | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const { walletType } = useGlobalState();

  const fetchData = useCallback(async () => {
    if (!user_wallet || !enabled || walletType === WalletType.NONE) return;

    setLoading(true);
    setError(null);

    try {
      // 从 localStorage 获取 API 凭证
      const creds = getItem("poly_clob_api_key_map") || {};

      // 检查是否有该地址的凭证
      if (!creds[user_wallet]) {
        setError("No credentials found for this wallet");
        setData(null);
        return;
      }

      const result = await getPointsAndFees(creds, walletType);
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch points and fees");
      console.error("Error in usePointsAndFees:", err);
    } finally {
      setLoading(false);
    }
  }, [user_wallet, enabled, walletType]);

  const refetch = useCallback(async () => {
    await fetchData();
  }, [fetchData]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refetch,
  };
};

/**
 * Hook for fetching points and fees with manual trigger (L2Header型接口)
 * @returns UsePointsAndFeesReturn with manual fetch function
 */
export const usePointsAndFeesLazy = () => {
  const [data, setData] = useState<PointsAndFeesData | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const { walletType } = useGlobalState();

  const fetchPointsAndFees = useCallback(
    async (user_wallet: string) => {
      if (walletType === WalletType.NONE) {
        setError("No wallet connected");
        return null;
      }

      setLoading(true);
      setError(null);

      try {
        // 从 localStorage 获取 API 凭证
        const creds = getItem("poly_clob_api_key_map") || {};

        // 检查是否有该地址的凭证
        if (!creds[user_wallet]) {
          setError("No credentials found for this wallet");
          return null;
        }

        const result = await getPointsAndFees(creds, walletType);
        setData(result);
        return result;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to fetch points and fees";
        setError(errorMessage);
        console.error("Error in usePointsAndFeesLazy:", err);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [walletType],
  );

  const refetch = useCallback(async () => {
    // 由于新的数据结构没有user_wallet字段，我们需要传入当前的user_wallet参数
    // 这个函数主要用于手动刷新，所以我们可以简化逻辑
    console.log("Manual refetch requested for lazy hook");
  }, []);

  return {
    data,
    loading,
    error,
    fetchPointsAndFees,
    refetch,
  };
};
