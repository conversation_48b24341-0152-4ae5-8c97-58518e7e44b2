{"translation": {"_comment_normal": "normal i18n content", "Normal_Markets": "Markets", "Normal_Election": "Election", "Normal_Activity": "Activity", "Normal_Ranks": "Ranks", "Normal_Sports": "Sports", "Normal_Connect_Wallet": "Connect Wallet", "Normal_See_all": "See all", "Normal_Copy": "Copy", "Normal_Copied": "<PERSON>pied", "Normal_Address": "Address", "Normal_Amount": "Amount", "Normal_Available": "Available", "Normal_Back": "Back", "Normal_Done": "Done", "Normal_Login": "<PERSON><PERSON>", "Normal_Google_Login": "Google Login (Best Choice)", "Normal_Login_Loading": "Logging in...", "Normal_Login_Failed": "<PERSON><PERSON> failed, retry", "Normal_Portfolio": "Portfolio", "Normal_Balance": "Balance", "Normal_Cash": "Cash", "Normal_Deposit": "<PERSON><PERSON><PERSON><PERSON>", "Normal_Withdraw": "Withdraw", "Normal_Other": "Other", "Normal_Wallets": "Wallets", "Normal_Continue": "Continue", "Normal_Close": "Close", "Wallet_Points_Title": "POINTS & MULTIPLIER", "Wallet_Points_Tooltip": "Earn points through trading activities. Multiplier increases your earning rate.", "Wallet_Fees_Title": "TRADING FEES", "Wallet_Total_Fees": "Total Fees", "Wallet_Claimable_Fees": "Claimable", "Wallet_Claiming_Fees": "Claiming Fees...", "Wallet_Claim_Fees": "<PERSON><PERSON><PERSON>", "Wallet_Claim_Success": "Fees claimed successfully!", "Wallet_Claim_Failed": "Failed to claim fees, please retry", "Wallet_Claim_Error": "Error occurred during claiming, please retry", "Wallet_No_Fees": "No Fees to <PERSON><PERSON><PERSON>", "Wallet_Ready_To_Claim": "Ready", "Wallet_Latest_Claim_Time": "Latest Claim Time", "Wallet_No_Permission": "No Permission", "Wallet_Need_Signature": "Signature authorization required to view points and fees data", "Wallet_Sign_Authorization": "Sign Authorization", "Wallet_Signature_Coming_Soon": "Signature feature coming soon", "Wallet_Fees_Tooltip": "Trading fees can be claimed after 30 days. Simply click the button to withdraw.", "Wallet_Points_System_Title": "Points Level System", "Wallet_Points_System_Description": "User levels are determined by monthly trading volume. Higher levels earn greater point multipliers.", "Wallet_Level_Name": "Level Name", "Wallet_Monthly_Volume": "Monthly Volume (USD)", "Wallet_Points_Multiplier": "Points Multiplier", "Wallet_Level_1": "Insight Novice", "Wallet_Level_2": "<PERSON><PERSON><PERSON>", "Wallet_Level_3": "Visionary Sage", "Wallet_Level_4": "Strategy Master", "Wallet_Level_5": "Future Architect", "Wallet_Level_6": "Time Lord", "Wallet_Level_7": "Legendary Prophet", "Wallet_Points_Calculation": "Points Calculation", "Wallet_Points_Formula": "User Points = Monthly Trading Volume × Level Multiplier", "Wallet_Settlement_Cycle": "Settlement Cycle", "Wallet_Settlement_Description": "Levels and settlement points are updated every 28 days based on the transaction volume of the previous month.", "Wallet_Fees_System_Title": "Trading Fees (Anti-Sybil Mechanism)", "Wallet_Fees_System_Description": "To ensure platform fairness and prevent malicious volume manipulation, we implement a 'deposit-style' fee system that is permanently free for genuine users.", "Wallet_Temporary_Collection": "Temporary Collection", "Wallet_Temporary_Collection_Description": "All trades temporarily incur a 1% fee.", "Wallet_Temporary_Collection_Badge": "Temporary 1% fee", "Wallet_Full_Refund": "Full Refund", "Wallet_Full_Refund_Description": "This fee is not platform revenue. Users can apply for a full 100% refund of all fees paid in the previous month through the personal center next month after platform review.", "Wallet_Full_Refund_Badge": "100% Full Refund", "Wallet_Free_For_Real_Users": "Permanently Free for Real Users", "Wallet_Free_For_Real_Users_Description": "This mechanism aims to prevent malicious volume manipulation. For genuine trading users, all fees will be fully refunded after review, achieving truly zero-cost trading.", "Normal_Zone_Warning_Title": "Restricted Territory", "Normal_Zone_Warning": "Trading is not available to people or companies who are residents of, or are located, incorporated or have a registered agent in, the United States, Australia, China or a restricted territory.", "Normal_Zone_Warning_Terms": "Terms of Use", "Normal_Validating": "Validating", "Normal_Approval_Required": "Approval Required", "_comment_creator": "/creator", "Creator_Page_Title": "Community Creator Events", "Creator_Page_Description": "Discover and participate in community-created prediction markets. These events are created by our users and represent diverse perspectives from around the world.", "Creator_Events_Title": "Community Events", "Creator_Events_Badge": "User Created", "Creator_Events_Subtitle": "These prediction markets are created by community members. Participate with caution and do your own research.", "Events_Count": "events", "Creator_No_Events_Title": "No Community Events Yet", "Creator_No_Events_Description": "Be the first to create a community prediction market! Community events will appear here once they are created.", "Loading_Creator_Events": "Loading creator events...", "Loading_More_Events": "Loading more events...", "Community": "Community", "Community_Short": "Com", "Creator": "Creator", "New": "NEW", "Result": "Result", "_comment_home": "/", "Home_Recent_Activity": "Recent Activity", "Home_Top_Volume_This_Week": "Top Volume This Week", "_comment_wallet": "/wallet", "Wallet_OTHER_METHODS": "OTHER METHODS", "Wallet_Chat_with_a_human": "Chat with a human", "Wallet_TUTORIALS": "TUTORIALS", "Wallet_I_don't_have_any_crypto": "I don't have any crypto", "Wallet_EASIEST METHOD": "EASIEST METHOD", "Wallet_1_MINUTE_FREE": "1 MINUTE · FREE", "Wallet_Deposit_USDC": "Deposit USDC (Base)", "Wallet_Buy_USDC": "Buy USDC: ", "Wallet_Buy_Path": "on Coinbase, Binance or another ", "Wallet_Exchange": "Exchange", "Wallet_Send_Withdraw": "Activate Your Account: ", "Wallet_Send_Text": "Deposit minimum $1 to activate your account", "Wallet_Sent_Methods": "USDC to the address below and select ", "Wallet_Send_Modal_Title": "Send USDC(Base)", "Wallet_BALANCE": "BALANCE", "Wallet_Withdraw": "Withdraw", "Wallet_Deposit": "<PERSON><PERSON><PERSON><PERSON>", "Wallet_Claim": "<PERSON><PERSON><PERSON>", "Wallet_Enable_Trading": "Enable Trading", "Wallet_Enable_Trading_Text": "In order to easily and securely trade on PredictOne, you will need to set up a proxy wallet", "_comment_withdraw_modal": "/wallet - withdraw_modal", "Withdraw_Modal_Tips": "Only send to a USDC address on the Base network.", "Withdraw_Modal_Use_Connected": "Use connected", "Withdraw_Modal_Withdraw_Failed": "Withdraw failed", "Withdraw_Modal_Withdraw_Error": "There are a lot of withdrawals happening right now and yours was unsuccessful due to high congestion.Please try again later or break your withdrawal down into smaller chunks of USDC.", "Withdraw_Modal_Deposit_Error": "Depo<PERSON><PERSON> failed", "Withdraw_Modal_Withdraw_Completed": "Withdraw completed", "Withdraw_Modal_Your_Funds": "Your funds ", "Withdraw_Modal_Were_Success": "were successfully transferred.", "Withdraw_Modal_Min_Value_Error": "The minimum withdrawal amount is 1 USDC.", "Withdraw_Modal_Max_Value_Error": "The withdrawal amount exceeds the available balance.", "Withdraw_Modal_Account_Blocked_Error": "Your account is not qualified for withdrawal. The account may be blocked or not enabled.", "Withdraw_Modal_Promotion_Requirement_Error": "Withdrawal failed: Promotion requirements not met. Current balance: ${{currentBalance}}, Current volume: ${{currentVolume}}. Minimum balance: ${{requiredBalance}}, Required volume: ${{requiredVolume}}.", "Withdraw_Modal_Promotion_General_Error": "<PERSON><PERSON><PERSON> failed: You have not met the requirements for the current promotion event.", "Withdraw_Modal_Balance_Insufficient_Error": "The remaining balance after this withdrawal request will be ${{leftOver}}, while the minimum required balance is ${{minBalance}}.", "_comment_deposit_modal": "/wallet - deposit_modal", "Deposit_Modal_Tips": "Only send to a USDC address on the Base network.", "Deposit_Modal_Use_Connected": "Use connected", "Deposit_Modal_Deposit_Failed": "Depo<PERSON><PERSON> failed", "Deposit_Modal_Deposit_Error_Described": "Your deposit was unsuccessful due to network congestion or the deposit amount exceeds your balance. Please try again later or break your deposit down into smaller chunks of USDC.", "Deposit_Modal_Deposit_Error": "Depo<PERSON><PERSON> failed", "Deposit_Modal_Deposit_Completed": "Deposit completed", "Deposit_Modal_Your_Funds": "Your funds ", "Deposit_Modal_Were_Success": "were successfully transferred.", "Deposit_Modal_Min_Value_Error": "The minimum deposit amount is 1 USDC.", "Deposit_Modal_Insufficient_Balance_Error_1": "The balance is insufficient, please top up ", "Deposit_Modal_Insufficient_Balance_Error_2": " or click Refresh", "Deposit_Modal_Step_One": "Please deposit your USDC(BASE) to the below address", "Deposit_Modal_Step_Two": "Enable account", "Deposit_Modal_Step_Three": "Minimal desposit: 1 USDC", "Deposit_Modal_Step_Two_Tip": "After successful recharge, please click the refresh icon to update the balance", "_comment_portfolio": "/portfolio", "portfolio": {"click_to_reload": "Click to reload", "loading": "Loading...", "frozen": "Frozen"}, "Portfolio_Positions": "Positions", "Portfolio_Open_Orders": "Open Orders", "Portfolio_History_Orders": "History", "Portfolio_Tabel_MARKET": "MARKET", "Portfolio_Tabel_Side": "Side", "Portfolio_Tabel_Outcome": "Outcome", "Portfolio_Tabel_Position": "Position", "Portfolio_Tabel_Price": "Price", "Portfolio_Tabel_Filled": "Filled", "Portfolio_Tabel_Total": "Total", "Portfolio_Tabel_Expiration": "Expiration", "Portfolio_Tabel_LATEST": "LATEST", "Portfolio_Tabel_BET": "BET", "Portfolio_Tabel_CURRENT": "CURRENT", "Portfolio_Tabel_TO_WIN": "TO WIN", "Portfolio_Tabel_Type": "Type", "Portfolio_Tabel_Shares": "Shares", "Portfolio_Tabel_Value": "Value", "Portfolio_Tabel_Date": "Date", "Portfolio_Filter_Hidden_Less_Dollar": "Hidden less $1", "Portfolio_Loading": "Loading...", "Portfolio_No_More_Data": "No more data", "Portfolio_No_Data": "No data", "Portfolio_Profit": "Profit", "_comment_portfolio_drop": "/portfolio - dropdown", "Portfolio_Drop_Current_Value": "Current value", "Portfolio_Drop_Initial_Value": "Initial value", "Portfolio_Drop_Return_$": "Return($)", "Portfolio_Drop_Return_%": "Return(%)", "Portfolio_Drop_Market": "Market", "Portfolio_Drop_Shares": "Shares", "Portfolio_Drop_Expiration": "Expiration", "Portfolio_Drop_To_Win": "To Win", "Portfolio_Drop_All": "All", "Portfolio_Drop_Active": "Active", "Portfolio_Drop_Closed": "Closed", "Portfolio_Drop_All_Trades": "All Trades", "Portfolio_Drop_Buy": "Buy", "Portfolio_Drop_Sell": "<PERSON>ll", "Portfolio_Drop_Merge": "<PERSON><PERSON>", "Portfolio_Drop_Redeem": "Redeem", "Portfolio_Drop_Reward": "<PERSON><PERSON>", "Portfolio_Drop_Split": "Split", "Portfolio_Drop_Convert": "Convert", "Portfolio_Drop_Newest": "Newest", "Portfolio_Drop_Oldest": "Oldest", "Portfolio_Drop_Value": "Value", "Portfolio_Drop_Export": "Export", "OpenOrder_Confirm_Delete_Title": "Delete Order", "OpenOrder_Confirm_Delete_Content": "Are you sure you want to delete this order?", "OpenOrder_Confirm_Delete_Cancel": "Cancel", "OpenOrder_Confirm_Delete_Confirm": "Confirm", "_comment_portfolio_date_filter": "/portfolio - date_filter", "Portfolio_Date_Filter": "Filter", "Portfolio_Date_Filter_All": "All", "Portfolio_Date_Filter_Today": "Today", "Portfolio_Date_Filter_Yesterday": "Yesterday", "Portfolio_Date_Filter_Last_Week": "Last Week", "Portfolio_Date_Filter_Last_Month": "Last Month", "Portfolio_Date_Filter_Last_3_Months": "Last 3 Months", "Portfolio_Date_Filter_Year_to_Date": "Year to Date", "Portfolio_Date_Filter_Last_Year": "Last Year", "_comment_markets": "/markets", "Markets_Sort_Trending": "Trending", "Markets_Sort_Liquidity": "Liquidity", "Markets_Sort_Volume": "Volume", "Markets_Sort_Newest": "Newest", "Markets_Sort_EndingSoon": "Ending Soon", "Markets_Sort_Competitive": "Competitive", "Markets_Status_All": "All", "Markets_Status_Active": "Active", "Markets_Status_Resolved": "Resolved", "_comment_event": "/event/xxx", "Event_OrderBook": "Order Book", "Event_OrderBook_Asks": "Asks", "Event_OrderBook_Bids": "Bids", "Event_OrderBook_Last": "Last", "Event_OrderBook_Spread": "Spread", "Event_OrderBook_Tabel_Price": "PRICE", "Event_OrderBook_Tabel_SHARES": "SHARES", "Event_OrderBook_Tabel_Total": "TOTAL", "Event_OrderBook_Tips": "Earn rewards by placing limit orders near the midpoint", "Event_OrderBook_Tips_Rewards": "Rewards", "Event_OrderBook_Tips_MaxSpread": "<PERSON>read", "Event_OrderBook_Tips_MinShares": "Min Shares", "Event_Order_Outcome": "Outcome", "Event_Order_LimitPrice": "<PERSON>it <PERSON>", "Event_Order_Share": "Share", "Event_Order_Total": "Total", "Event_Order_PotentialReturn": "Potential return", "Event_Order_Available": "available", "Event_Order_Available_Tips": "Available to trade", "Event_Order_PopText1": "By trading, you agree to the", "Event_Order_PopText2": "Terms of Use", "Event_Order_PopText3": "By trading, you agree to the Terms of Use including that you are not (i) a U.S. person and (ii) located in the United States or other restricted territory.", "Order_Insufficient_Balance_Title": "Insufficient Balance", "Order_Insufficient_Balance_Content": "Order amount ${{required}} exceeds available balance ${{available}}. Please reduce order amount or add funds.", "Order_Insufficient_Shares_Title": "Insufficient Shares", "Order_Insufficient_Shares_Content": "You don't have enough shares to complete this order. Required: {{required}}, Available: {{available}}", "market_order": {"amount": "Amount", "shares": "Shares", "available_balance": "Available ${{balance}}", "available_shares": "Shares {{shares}}", "getting_quote": "Getting Quote...", "unable_to_get_quote": "Unable to Get Quote", "to_win": "To Win", "to_receive": "To Receive", "avg_price": "Avg. Price {{price}}¢", "return_percentage": "+{{percentage}}% Return", "input_amount": "Input Amount", "actual_cost": "Cost", "shares_count": "Shares", "processing": "Processing...", "no_shares_available": "No Shares Available", "buy_at_market_price": "Buy at Market Price", "sell_at_market_price": "Sell at Market Price", "at_market_price": "{{action}} at Market Price", "buy": "Buy", "sell": "<PERSON>ll", "max": "Max", "quick_amounts": {"add_1_dollar": "+$1", "add_20_dollars": "+$20", "add_100_dollars": "+$100", "add_10_shares": "+10", "add_25_shares": "+25", "add_50_shares": "+50"}, "errors": {"network_error": "Failed to get quote, please check network connection or try again later", "insufficient_liquidity_buy": "Insufficient market liquidity, please reduce purchase amount or use limit order", "insufficient_liquidity_sell": "Insufficient market liquidity, please reduce sell shares or use limit order", "orderbook_unavailable": "Market orderbook temporarily unavailable, please try again later", "amount_required": "Amount is required", "shares_required": "Shares required", "invalid_quote": "Unable to get valid quote, please check input amount", "invalid_quote_retry": "Unable to get valid quote, please re-enter amount", "minimum_shares": "Order shares must be greater than or equal to 5", "minimum_amount": "Minimum amount is ${{amount}}", "minimum_order_value": "Order amount must be greater than or equal to $1", "maximum_shares": "Maximum shares available: {{shares}}", "minimum_share_required": "Minimum 1 share required"}, "suggestions": {"reduce_amount_or_limit": "Suggestion: Try reducing {{type}} or use limit order trading", "buy_amount": "purchase amount", "sell_shares": "sell shares"}}, "promotion": {"title": "🎉 Congratulations! Special <PERSON><PERSON> Unlocked!", "subtitle": "Thank you for being such a valuable part of Predict.One!", "offers": {"offer1": "6 MONTHS OF ABSOLUTELY FREE TRADING!", "offer2": "Keep earning points from trading volume for more amazing rewards!"}, "congratsMessage": "Wow, Ox**230! Reaching 10,000 in such a short time is incredible! We're so grateful for your enthusiasm for Predict.One that we're giving you 6 MONTHS OF ABSOLUTELY FREE TRADING!", "details": "Buy, sell, trade as much as you want – no fees, for half a year! And the best part? Your trading volume will keep earning you points towards even more amazing surprise rewards."}, "promotion2": {"title": "🎉 Predict.one's MEGA GIVEAWAY!", "subtitle": "🎁 Grab Your FREE $50 USDC Bonus!", "offers": {"offer1": "✨ Ding! Your FREE $50 USDC is ready! 💸 One-click connect with your favorite social accounts (Google, Twitter & more – all good!)!", "offer2": "Deposit Just $1 ➔ UNLOCK $50 USDC!", "offer3": ""}, "whyus": "Why Us?", "whyus_list": {"item1": "✅ No KYC! Your Privacy First.", "item2": "🚀 Instant Access! Predict Now.", "item3": "💸 Bonus Cash = Bigger Wins!"}, "buttons": {"cta_primary": "Claim Now (Offer Ends Soon!)"}}, "rewards": {"claim": "<PERSON><PERSON><PERSON>", "claiming": "Claiming...", "claimSuccess": "Claim successful!", "claimSuccessMessage": "Congratulations! please claim your pre-reward.", "claimFailure": "<PERSON><PERSON><PERSON> failed.", "claimFailureError": "Collection failed. Please try again later.", "claimedAmount": "You have successfully claimed {{amount}} USD.", "needMoreVolume": "You need {{volume}} {{unit}} more to claim.", "title": "Rewards Progress", "loading": "Loading...", "completed": "completed", "pendingReward": "Pending Reward", "totalRewarded": "Total Rewarded", "lastClaimTime": "Last Claim Time", "remainingVolume": "Remaining Volume to Next Claim", "transactions": "transactions volume", "unknown": "Unknown"}, "login": {"modal": {"title": "Welcome to PredictOne", "or": "OR"}, "google": {"title": "Google Account", "description": "Sign in with your Google account"}, "twitter": {"title": "Twitter Account", "description": "Sign in with your Twitter account"}, "discord": {"title": "Discord Account", "description": "Sign in with your Discord account"}, "wallet": {"title": "Crypto Wallet", "description": "Connect with MetaMask", "step1": {"title": "Step 1: Link Google/Twitter& Google Account! 🎉"}, "step2": {"title": "Step 2: Skyrocket it to $50! 💵", "description": "Next, after logging in, you'll land on the deposit page. Deposit just 1 USDC to unlock an incredible $50 USDC in free credit! 🚀"}}, "terms": "Terms · Privacy", "steps": {"oauth_callback": "Processing OAuth callback...", "get_user_info": "Getting user information...", "generate_signature": "Generating signature message...", "verify_signature": "Verifying signature...", "login_verification": "Login verification...", "check_proxy_wallet": "Checking proxy wallet...", "create_proxy_wallet": "Creating proxy wallet...", "processing": "Processing...", "check_token_approval": "Checking token approval status...", "approve_token": "Approving token...", "enable_trading": "Enabling trading features...", "complete_setup": "Completing setup...", "init_magic": "Initializing Magic..."}, "errors": {"get_user_info": "Unable to get user information", "login_verification": "Login verification failed", "create_proxy": "Proxy wallet creation failed, please try again", "magic_timeout": "Magic initialization timeout", "trading_failed": "Enabling trading features failed:"}}, "nav": {"home": "Home", "markets": "Markets", "activity": "Activity", "mystery": "Mystery Box", "more": "More", "portfolio": "Portfolio", "addFunds": "Add Funds", "rank": "Rank", "walletUser": "Wallet User", "proxyWallet": "Proxy Wallet", "loading": "Loading...", "copied": "<PERSON>pied", "disconnect": "Disconnect", "disconnecting": "Disconnecting...", "errors": {"getProxyWalletFailed": "Failed to get proxy wallet:", "copyFailed": "Failed to copy address:", "fallbackCopyFailed": "Fallback copy method also failed:", "logoutFailed": "Logout failed:"}}, "sports": {"all_sports": "All Sports", "basketball": "Basketball", "soccer": "Soccer", "no_events_available": "No events available", "loading_events": "Loading events...", "props_view": "Props View", "game_view": "Game View", "no_game_events": "No game events available", "no_props_events": "No props events available", "no_game_events_description": "No match events configured for this sport"}, "hot_events": "Hot Events", "related_events": "Related Events", "media": "Media", "no_data": "No data available", "view": "View", "no_related_events_found": "No related events found", "loading_related_events": "Loading related events...", "loading_more_related_events": "Loading more related events...", "no_more_related_events": "No more related events", "activityBanner": {"anonymous": "A User", "loading": "Loading...", "buy": "bought", "sell": "sold", "priceAction": "at {{price}}¢ {{action}}"}, "chart": {"loading": "Loading chart data...", "no_data_title": "No Chart Data", "no_data_desc": "There is currently no available price history data for this time range. Please try another time range or check back later."}, "redpocket": {"title": "Enter Redemption Code", "inputPlaceholder": "Input exclusive invitation code", "claimButton": "Redeem <PERSON>ock<PERSON>", "processing": "Processing request...", "securityTip": "Important Notes", "googleLoginTip": "Please authenticate via Google Account", "successTitle": "🎉 Redemption Successful!", "successMessage": "You are the architect of your financial destiny", "directReward": "Immediate Reward", "tradingReward": "Trading Bonus", "totalReward": "Total Rewards", "tradingRewardNote": "Unlocked upon reaching trading volume threshold", "successNote": "RedPocket rewards have been added to the prize pool, please click the Prize Pool button to claim", "withdrawAnytime": "Withdrawable at any time", "availableForPrediction": "Applicable to platform prediction activities", "goToFuture": "<PERSON><PERSON><PERSON>, Command Wealth", "failedTitle": "Redemption Failed", "errorInfo": "<PERSON><PERSON><PERSON>", "errorAlreadyOpened": "RedPocket already claimed. Join our next event!", "errorInvalidCode": "Invalid redemption code", "errorTooLate": "You come too late", "networkErrorTip": "Please re-authenticate via Google Account to complete redemption", "retryButton": "Retry Redemption", "goToHome": "Go to Home", "hours": "Hrs", "minutes": "<PERSON>s", "seconds": "Secs", "activityEnded": "Thank you for participating. New events coming soon!", "countdownTitle": "Countdown to the next round of RedPocket", "initializing": "Initializing...", "eventEndedTitle": "🎊 Event Ended", "eventEndedInfo": "Event Status", "eventEndedMessage": "This RedPocket event has successfully ended. Thank you for your attention and participation! Please look forward to our next exciting event.", "eventNotStarted": "Event Not Started", "activityStarted": "Activity Started!", "days": "Days"}, "mysteryBox": {"title": "Mystery Box", "section": {"myBoxes": "My Boxes", "totalBoxes": "Total {{count}} Boxes", "loading": "Loading...", "checkingRewards": "Checking rewards...", "loadingFailed": "Loading Failed: {{error}}", "retry": "Retry", "noBoxes": "No Boxes Available", "noBoxesDesc": "Connect wallet or invite friends to earn boxes", "checkRewards": "View Rewards", "title": "Mystery Box", "subtitle": "Each box contains real USDC rewards from $0.01 to $10. Open now to discover your luck!", "readyCount": "Available", "cooldownCount": "Cooldown", "totalCount": "Total", "mysteryBoxTitle": "Mystery Box"}, "card": {"ready": "Claimable", "cooldown": "Cooldown", "opening": "Opening...", "openBox": "🎁 Open Now", "waitForOtherBox": "Please wait for other box to finish opening"}, "modal": {"congratulations": "Congratulations!", "rewardSent": "Reward sent to your wallet", "thankYou": "Thank you! Invite friends for more rewards"}, "history": {"title": "Opening History", "connectWallet": "Connect wallet to view history", "noRecords": "No opening records", "firstBox": "Open Your First Box", "time": "Time", "reward": "<PERSON><PERSON>", "txHash": "Transaction Hash", "status": "Status", "processing": "Processing", "completed": "Completed", "totalOpened": "Total Opens", "totalRewards": "Total Rewards", "locale": "en-US"}, "referral": {"title": "Referral Program", "inviteCode": "Invite Code", "inviteLink": "Invite Link", "copyCode": "Copy Code", "copyLink": "Copy Link", "copied": "Copied!", "activateAccount": "Activate Account", "activating": "Activating...", "invitedUsers": "Invited Users", "noInvites": "No invitations yet", "startInviting": "Start Inviting Friends", "username": "Username", "wallet": "Wallet", "rewardTitle": "Referral Rewards", "rewardDescription": "Earn {{count}} bonus boxes per successful referral", "processTitle": "How It Works", "processSubtitle": "Earn rewards in 3 steps", "step1Title": "Share Link", "step1Description": "Copy your unique invite link and share with friends", "step2Title": "Friend Joins", "step2Description": "Friend registers via your link to get welcome box", "step3Title": "Get Re<PERSON>s", "step3Description": "Receive bonus box immediately after successful referral", "notSet": "Not Set", "rewards": {"noBoxes": "No mystery boxes", "unOpenedBoxes": "You have {{count}} unopened mystery boxes", "openedBoxes": "Opened {{count}} mystery boxes", "invitedUser": "Invited user", "invitedFriends": "Invited {{count}} friends", "separator": ", ", "systemActivated": "Mystery box system activated"}}, "rewards": {"reasonSeparator": ", ", "congratulations": "🎉 Congratulations on your reward!\nYou received {{count}} mystery boxes!", "newBoxes": "Received {{count}} new reward mystery boxes!", "noNewBoxes": "No new reward mystery boxes available", "checkFailed": "Failed to check rewards"}, "errors": {"boxNotFound": "Box not found", "boxCooldown": "Box in cooldown, available in {{time}}", "openFailed": "Open failed, please retry", "userCancelled": "User cancelled signing", "requestTimeout": "Request timeout, check network", "signatureVerificationFailed": "Signature verification failed", "claimFailed": "<PERSON><PERSON><PERSON> failed: {{error}}", "unknownError": "Unknown error"}, "toast": {"processing": "Processing request...", "successReward": "🎉 Successfully claimed {{amount}} USDC!", "openFailed": "Open failed, please retry"}, "metamaskNotSupported": {"title": "MetaMask Not Supported", "description": "This feature is not available for MetaMask", "switchTip": "Please switch to other login methods", "suggestions": "Steps:", "step1": "• Disconnect MetaMask", "step2": "• Choose other login methods (Google/Twitter)", "step3": "• Re-enter mystery box page", "goBack": "Go Back"}, "welcome": {"brandTitle": "USDC Mystery Box", "mainTitle": "Start Your", "mainTitleHighlight": "Mystery Box", "mainTitleEnd": "Journey", "subtitle": "Get your first free box with real USDC rewards (0.01-10 USDC)!", "startButton": "Get Started", "features": {"freeJoin": "Free to Join", "realRewards": "Real USDC Rewards", "dailyChances": "Daily Opportunities"}, "howItWorks": {"title": "How to Play", "subtitle": "Earn rewards in 3 steps", "step1": {"title": "Connect Account", "description": "Click \"Get Started\" to connect via Google/Twitter/Discord"}, "step2": {"title": "Open Box", "description": "Get your first free box instantly after login"}, "step3": {"title": "Invite Friends", "description": "Share invite link to earn bonus boxes"}}}}, "claimReward": {"title": "Prize Pool", "loading": "Loading reward data...", "totalAmount": "Total Claimable Amount", "itemsCount": "{{count}} items total", "clickToClaim": "Click to claim all", "noRewards": "No rewards available", "completeActivities": "Complete more activities to earn rewards", "claiming": "Claiming...", "noRewardsButton": "No rewards to claim", "claimButton": "Claim All", "otherRewards": "Other Rewards", "noRewardsAvailable": "No rewards available to claim", "claimSuccess": "<PERSON><PERSON><PERSON> successfully claimed to your account", "claimFailedRetry": "<PERSON><PERSON><PERSON> failed, please try again later", "insufficientAmount": "Claim failed, at least 1 USDC required to claim", "networkError": "Network error, please check your connection", "requestFailed": "Request failed", "claimFreeRewards": "Claim Your Free Rewards Now!"}}}