@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";
@import "~@glidejs/glide/dist/css/glide.core.min.css";
@import "~@glidejs/glide/dist/css/glide.theme.min.css";
/* 引用 Google Fonts */
@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap");

/* 引用 Google Fonts */
@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap");

:root,
[data-theme] {
  /* background: white; */
}

body {
  min-height: 100vh;
  font-family: "Roboto", Helvetica, "Trebuchet MS", Verdana, sans-serif;
  overflow-x: hidden; /* 防止页面级横向滚动 */
}

html {
  overflow-x: hidden; /* 防止页面级横向滚动 */
}

h1,
h2,
h3,
h4 {
  margin-bottom: 0.5rem;
  line-height: 1;
}

p {
  margin: 1rem 0;
}

.btn {
  @apply shadow-md;
}

.btn.btn-ghost {
  @apply shadow-none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.no-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

@layer utilities {
  .min-w-col-3 {
    min-width: calc(100% / 3 - 12px);
  }
  .min-w-col-4 {
    min-width: calc(100% / 4 - 12px);
  }
}
/* 隐藏数字输入框的加减按钮 */
.no-spinner::-webkit-outer-spin-button,
.no-spinner::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.no-spinner {
  -moz-appearance: textfield;
}
/* 移除输入框聚焦时的蓝色边框 */
.no-focus-outline:focus {
  outline: none;
  box-shadow: none;
}

@keyframes square-spin {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(90px, 0);
  }
  50% {
    transform: translate(90px, 90px);
  }
  75% {
    transform: translate(0, 90px);
  }
  100% {
    transform: translate(0, 0);
  }
}

.spinner-square {
  animation: square-spin 2s linear infinite;
}

.italic-text {
  font-family: sans-serif;
  font-style: italic;
}
