import { PointsAndFeesData } from "@/api/user/pointsAndFees";

/**
 * 转换积分和手续费数据的工具函数
 */
export interface ProcessedPointsAndFeesData {
  currentPoints: number;
  pointsMultiplier: number;
  totalFees: number;
  feeGained: number;
  claimableFees: number;
  latestClaimTime: string | null;
}

/**
 * 处理和转换积分手续费数据
 * @param pointsFeesData 原始API数据
 * @returns 处理后的数据
 */
export const processPointsAndFeesData = (pointsFeesData: PointsAndFeesData | null): ProcessedPointsAndFeesData => {
  if (!pointsFeesData) {
    return {
      currentPoints: 0,
      pointsMultiplier: 1.0,
      totalFees: 0,
      feeGained: 0,
      claimableFees: 0,
      latestClaimTime: null,
    };
  }

  const currentPoints = pointsFeesData.credit ? Math.floor((parseFloat(pointsFeesData.credit) / 1000000) * 100) : 0;

  const pointsMultiplier = pointsFeesData.credit_ratio ? parseFloat(pointsFeesData.credit_ratio) : 1.0;

  const totalFees = pointsFeesData.fee ? parseFloat(pointsFeesData.fee) / 1000000 : 0;

  const feeGained = pointsFeesData.fee_gained ? parseFloat(pointsFeesData.fee_gained) / 1000000 : 0;

  const claimableFees = Math.max(0, feeGained);
  const latestClaimTime = pointsFeesData.latest_claim_time || null;

  return {
    currentPoints,
    pointsMultiplier,
    totalFees,
    feeGained,
    claimableFees,
    latestClaimTime,
  };
};

/**
 * 格式化USDC金额显示
 * @param amount 金额
 * @param decimals 小数位数，默认2位
 * @returns 格式化后的字符串
 */
export const formatUSDCAmount = (amount: number, decimals = 2): string => {
  return `$${amount.toFixed(decimals)}`;
};

/**
 * 检查API错误是否为28天限制错误
 * @param error 错误对象
 * @returns 是否为28天限制错误
 */
export const is28DayLimitError = (error: any): boolean => {
  const errorMessage = error?.response?.data?.message || error?.message || "";
  return errorMessage.includes("claimed the fee within 28 days");
};

/**
 * 从API响应中提取领取金额
 * @param response API响应
 * @returns 领取的金额（USDC单位）
 */
export const extractClaimedAmount = (response: any): number => {
  if (!response?.data?.amount_send) return 0;
  return parseFloat(response.data.amount_send) / 1000000;
};
