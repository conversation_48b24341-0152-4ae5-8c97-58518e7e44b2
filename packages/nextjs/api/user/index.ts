import { initRequest } from "../request";
import { WalletType } from "@/services/store/store";
import { getProviderAndSigner } from "@/utils";
import { createL2Headers } from "pedone-clob-client";

export async function getWebUsersByProxyWallet(proxy_wallet: string) {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/backGetWebUser`,
    method: "get",
    headers: { "Content-Type": "application/json" },
    params: {
      proxy_wallet: proxy_wallet,
    },
  });
}

export async function getRewardDistributionByProxyWallet(proxy_wallet: string) {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getRewardDistribution`,
    method: "get",
    headers: { "Content-Type": "application/json" },
    params: {
      proxy_wallet: proxy_wallet,
    },
  });
}

export async function getUserRewards(proxy_wallet: string) {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getUserRewards`,
    method: "get",
    headers: { "Content-Type": "application/json" },
    params: {
      proxy_wallet: proxy_wallet,
    },
  });
}

export async function claimAllRewards(creds: any, walletType: WalletType) {
  const signer = await getProviderAndSigner(walletType);
  if (!signer) {
    throw new Error("Signer is undefined");
  }

  const headerArgs = {
    method: "GET",
    requestPath: "/offchain-claim-reward",
  };
  const address = await signer.getAddress();

  // @ts-ignore
  const l2Headers = await createL2Headers(signer, creds[address], headerArgs);

  return initRequest({
    url: `${process.env.NEXT_PUBLIC_PDONE_ENDPOINT}/offchain-claim-reward`,
    method: "get",
    headers: {
      ...l2Headers,
    },
    params: {
      token: "usdc",
    },
  });
}

export async function getClaimReward(creds: any, walletType: WalletType) {
  const signer = await getProviderAndSigner(walletType);
  if (!signer) {
    throw new Error("Signer is undefined");
  }

  const headerArgs = {
    method: "GET",
    requestPath: "/claim-reward",
  };
  const address = await signer.getAddress();

  // @ts-ignore
  const l2Headers = await createL2Headers(signer, creds[address], headerArgs);

  return initRequest({
    url: `${process.env.NEXT_PUBLIC_PDONE_ENDPOINT}/claim-reward`,
    method: "get",
    headers: {
      ...l2Headers,
    },
  });
}
