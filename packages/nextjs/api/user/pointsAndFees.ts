import { initRequest } from "../request";
import { WalletType } from "@/services/store/store";
import { getProviderAndSigner } from "@/utils";
import { createL2Headers } from "pedone-clob-client";

// 积分和手续费数据类型
export interface PointsAndFeesData {
  fee: string;
  credit_ratio: string;
  credit: string;
  fee_gained: string;
  latest_claim_time: string;
}

/**
 * 获取用户积分和手续费数据 (L2Header型接口)
 * @param creds 用户凭证
 * @param walletType 钱包类型
 * @returns Promise<PointsAndFeesData | null>
 */
export const getPointsAndFees = async (creds: any, walletType: WalletType): Promise<PointsAndFeesData | null> => {
  const signer = await getProviderAndSigner(walletType);
  if (!signer) {
    throw new Error("Signer is undefined");
  }

  const headerArgs = {
    method: "GET",
    requestPath: "/query-claim-fee",
  };
  const address = await signer.getAddress();

  // @ts-ignore
  const l2Headers = await createL2Headers(signer, creds[address], headerArgs);

  try {
    const response = await initRequest({
      url: `${process.env.NEXT_PUBLIC_PDONE_ENDPOINT}/query-claim-fee`,
      method: "get",
      headers: {
        ...l2Headers,
      },
    });

    return response?.data?.data || null;
  } catch (error) {
    console.error("Error fetching points and fees:", error);
    throw error;
  }
};
