import { initRequest } from "./request";
import { topNewsList } from "@/api/mock";
import { getItem, setItem } from "@/utils";
import axios from "axios";

const getAllTagsListData = () => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getAllTags`,
    method: "get",
  });
};

const getAllTagsWithEventCount = (params?: { active?: boolean; closed?: boolean }) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getAllTagsWithEventCount`,
    method: "get",
    params: {
      active: params?.active ?? true,
      closed: params?.closed ?? false,
    },
  });
};

const getAllTagsWithCreatorEventCount = (params: { creatorTagId: number; active?: boolean; closed?: boolean }) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getAllTagsWithCreatorEventCount`,
    method: "get",
    params: {
      creatorTagId: params.creatorTagId,
      active: params.active ?? true,
      closed: params.closed ?? false,
    },
  });
};

const getSubHeaderListData = () => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getSubHeaderList`,
    method: "get",
  });
};

const TOP_NEWS_API_KEY_LIST = [
  "b48eb8cca3354ccdbd3f33cf03378eca",
  "c8db53eb3afb4c15beae5e5e308005fc",
  "918c9eb0f7cb4c2a979984bcc1f547cd",
];

const getCurrentApiKey = (): string => {
  const currentKeyIndex = getItem("currentNewsApiKeyIndex") || 0;
  return TOP_NEWS_API_KEY_LIST[currentKeyIndex];
};

const switchApiKey = (): void => {
  let currentKeyIndex = getItem("currentNewsApiKeyIndex") || 0;
  currentKeyIndex = (currentKeyIndex + 1) % TOP_NEWS_API_KEY_LIST.length;
  setItem("currentNewsApiKeyIndex", currentKeyIndex);
};

const getTopNewsListData = async (eventTitle: string, page: number) => {
  let apiKey = getCurrentApiKey();
  try {
    const response = await axios.get(
      `https://newsapi.org/v2/everything?q=${eventTitle}&apiKey=${apiKey}&page=${page}&pageSize=20`,
    );
    return response;
  } catch (error: any) {
    if (error.response && error.response.status === 426) {
      // 426 means that the number of results exceeds the limit for developer accounts
      switchApiKey();
      apiKey = getCurrentApiKey();
      const response = await axios.get(
        `https://newsapi.org/v2/everything?q=${eventTitle}&apiKey=${apiKey}&page=${page}&pageSize=10`,
      );
      return response;
    } else {
      throw error;
    }
  }
};

const getTopNewsListMockData = () => {
  return { data: topNewsList };
};

const getEventByKeyword = (keyword: string) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getEventByKeyword`,
    method: "get",
    params: {
      keyword: `%${keyword}%`,
    },
  });
};
const getEventListByIds = (ids: string[]) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getEventListByIds`,
    method: "post",
    data: {
      ids,
    },
  });
};

const getActivityBannerList = (limit = 20) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_PDONE_ENDPOINT}/last-trades?limit=${limit}`,
    method: "get",
  });
};

const getEventsByTagId = (params: { limit?: number; offset?: number; closed?: boolean; tag_id: number }) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getEventsByTagId`,
    method: "get",
    params: {
      limit: params.limit || 20,
      offset: params.offset || 0,
      closed: params.closed,
      tag_id: params.tag_id,
    },
  });
};

const getCreatorEvents = (params: { limit?: number; offset?: number; active?: boolean; closed?: boolean }) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getCreatorEvents`,
    method: "get",
    params: {
      limit: params.limit || 20,
      offset: params.offset || 0,
      active: params.active ?? true,
      closed: params.closed ?? false,
    },
  });
};

const getAllHomeEvents = (params: { limit?: number; offset?: number; active?: boolean; closed?: boolean }) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getAllHomeEvents`,
    method: "get",
    params: {
      limit: params.limit || 20,
      offset: params.offset || 0,
      active: params.active ?? true,
      closed: params.closed ?? false,
    },
  });
};

const getHomeEventsByTag = (params: {
  limit?: number;
  offset?: number;
  active?: boolean;
  closed?: boolean;
  tag_id: number;
}) => {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getHomeEventsByTag`,
    method: "get",
    params: {
      limit: params.limit || 20,
      offset: params.offset || 0,
      active: params.active ?? true,
      closed: params.closed ?? false,
      tag_id: params.tag_id,
    },
  });
};

export {
  getAllTagsListData,
  getAllTagsWithEventCount,
  getAllTagsWithCreatorEventCount,
  getSubHeaderListData,
  getTopNewsListMockData,
  getTopNewsListData,
  getEventByKeyword,
  getEventListByIds,
  getActivityBannerList,
  getEventsByTagId,
  getCreatorEvents,
  getAllHomeEvents,
  getHomeEventsByTag,
};
