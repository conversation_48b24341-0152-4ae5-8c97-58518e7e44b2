import React, { useCallback, useState } from "react";
import { getEventByKeyword, getEventListByIds } from "@/api/events";
import { base64DecodeByEn, base64DecodeByLanguage, getCurrentTimestamp } from "@/utils";
import { Image, Link } from "@heroui/react";
import { debounce } from "lodash";
import { MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import { useGlobalState } from "~~/services/store/store";

export const SearchBar = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [results, setResults] = useState([]);
  const { current_language } = useGlobalState().nativeCurrency;
  const timeStamp = getCurrentTimestamp();

  const fetchResults = async (keyword: string) => {
    if (!keyword.trim()) {
      setResults([]);
      return;
    }

    try {
      // Get a list of event Ids
      const keywordResponse = await getEventByKeyword(keyword);
      const keywordData = keywordResponse.data.search_event;
      const eventIds = keywordData.map((event: any) => event.event_id);
      if (eventIds.length === 0) {
        setResults([]);
        return;
      }

      // Get detailed event data
      const eventResponse = await getEventListByIds(eventIds);
      const eventData = eventResponse.data;

      const matchedEvents = eventData.events.map((event: any) => ({
        ...event,
        decodedTitle: base64DecodeByLanguage(event.title),
      }));

      setResults(matchedEvents);
    } catch (error) {
      console.error("Error fetching events:", error);
      setResults([]);
    }
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedFetchResults = useCallback(
    debounce((keyword: string) => fetchResults(keyword), 300),
    [],
  );

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const keyword = e.target.value;
    setSearchTerm(keyword);
    debouncedFetchResults(keyword);
  };

  return (
    <nav className="flex items-center text-base font-medium w-full px-4">
      <div className="relative flex flex-col md:flex w-full">
        <div className="hidden md:flex items-center w-full">
          <span className="absolute left-3 text-gray-500">
            <MagnifyingGlassIcon className="w-5 h-5" />
          </span>
          <input
            id="search-input"
            type="search"
            placeholder="Search markets"
            autoComplete="off"
            className="input input-bordered w-full pl-10"
            value={searchTerm}
            onChange={handleSearch}
          />
        </div>

        <div className="w-full absolute top-12 left-0 p-2">
          {results.length > 0 && (
            <div className="relative w-full bg-white shadow-lg rounded-md z-10">
              {results.map((event: any) => (
                <Link
                  key={event.id}
                  href={`/event/${base64DecodeByEn(event?.slug)}?tid=${event?.id}&conditionid=${
                    event?.event_markets[0]?.condition_id
                  }&lng=${current_language}&timeStamp=${timeStamp}`}
                  className="w-full text-sm font-semibold text-inherit hover:underline"
                >
                  <div className="flex w-full items-center px-5 py-2 border-b last:border-none hover:bg-gray-100">
                    <Image src={event.image} alt={event.decodedTitle} className="size-8 object-cover rounded-md mr-3" />
                    <div className="flex-1">
                      <div className="font-bold text-sm text-gray-800 line-clamp-2">{event.decodedTitle}</div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          )}
        </div>
      </div>
    </nav>
  );
};
