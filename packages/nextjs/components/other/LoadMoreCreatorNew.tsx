"use client";

import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { getAllHomeEvents, getHomeEventsByTag } from "@/api/events";
import { filterEventsByLanguage } from "@/utils";
import { useGlobalState } from "~~/services/store/store";

interface QueryParams {
  limit: number;
  offset: number;
  tag_id: string | number | null;
}

const useInfiniteScrollCreator = (initialParams: any, availableTagIds?: string[]) => {
  const [data, setData] = useState<any[]>([]);
  const [rawData, setRawData] = useState<any[]>([]); // 存储原始的Creator事件数据
  const [queryParams, setQueryParams] = useState<QueryParams>(initialParams);
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);
  const [initialLoad, setInitialLoad] = useState(true);
  const { current_language } = useGlobalState().nativeCurrency;

  // 客户端过滤函数
  const filterEventsByTag = useCallback(
    (events: any[], tagId: string) => {
      if (!events || events.length === 0) return [];

      if (tagId === "all") {
        // All模式：显示包含当前地区可用标签的Creator事件
        return events.filter((event: any) => {
          if (!event?.event_tags || !Array.isArray(event.event_tags)) {
            return false;
          }

          // 首先检查是否包含Creator标签
          const hasCreatorTag = event.event_tags.some((eventTag: any) => {
            const tagSlug = eventTag?.tag?.slug?.toLowerCase();
            return tagSlug === "creator";
          });

          if (!hasCreatorTag) {
            return false;
          }

          // 检查是否包含当前地区的可用标签
          if (!availableTagIds || availableTagIds.length === 0) {
            return true;
          }

          return event.event_tags.some((eventTag: any) => {
            const eventTagId = eventTag?.tag?.id?.toString() || eventTag?.tag_id?.toString();
            return eventTagId && availableTagIds.includes(eventTagId);
          });
        });
      } else {
        // 特定标签模式：显示同时包含Creator标签和指定标签的事件
        return events.filter((event: any) => {
          if (!event?.event_tags || !Array.isArray(event.event_tags)) {
            return false;
          }

          let hasCreatorTag = false;
          let hasSpecificTag = false;

          event.event_tags.forEach((eventTag: any) => {
            const tagSlug = eventTag?.tag?.slug?.toLowerCase();
            const eventTagId = eventTag?.tag?.id?.toString() || eventTag?.tag_id?.toString();

            if (tagSlug === "creator") {
              hasCreatorTag = true;
            }
            if (eventTagId === tagId.toString()) {
              hasSpecificTag = true;
            }
          });

          return hasCreatorTag && hasSpecificTag;
        });
      }
    },
    [availableTagIds],
  );

  const loadMore = useCallback(() => {
    if (isLoading) {
      return;
    }
    setQueryParams(prevParams => ({
      ...prevParams,
      offset: prevParams.offset + prevParams.limit,
    }));
  }, [isLoading]);

  const observer = useMemo(() => {
    if (typeof window === "undefined") return null;
    return new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting && hasMore && !initialLoad) {
          loadMore();
        }
      },
      {
        root: null,
        rootMargin: "20px",
        threshold: 1.0,
      },
    );
  }, [hasMore, loadMore, initialLoad]);

  // 监听语言变化，重置数据
  useEffect(() => {
    setData([]);
    setRawData([]);
    setHasMore(true);
    setInitialLoad(true);
    // 注意：不在这里设置queryParams，让CreatorEventCardList来统一处理
  }, [current_language]);

  // 处理Tag切换的客户端过滤
  useEffect(() => {
    console.log("🏷️ 客户端过滤触发", {
      rawDataLength: rawData.length,
      tagId: queryParams.tag_id,
    });

    if (rawData.length > 0 && queryParams.tag_id) {
      const filteredData = filterEventsByTag(rawData, queryParams.tag_id.toString());

      setData(filteredData);
    } else if (rawData.length === 0 && queryParams.tag_id) {
      // 如果rawData为空但tag_id已设置，清空显示数据
      setData([]);
    }
  }, [queryParams.tag_id, rawData, filterEventsByTag]);

  useEffect(() => {
    const fetchDataAsync = async () => {
      if (isLoading) {
        return;
      }

      setIsLoading(true);
      try {
        // 根据tag_id选择不同的API
        const apiParams = {
          limit: queryParams.limit,
          offset: queryParams.offset,
          active: true,
          closed: false,
        };

        let res;
        if (queryParams.tag_id === "all" || !queryParams.tag_id) {
          res = await getAllHomeEvents(apiParams);
        } else {
          res = await getHomeEventsByTag({
            ...apiParams,
            tag_id: Number(queryParams.tag_id),
          });
        }
        const allEvents = res.data.events || [];
        const filteredByLanguage = filterEventsByLanguage(current_language, allEvents);

        // 只过滤出包含Creator标签的事件，存储为原始数据
        const creatorEvents = filteredByLanguage.filter((event: any) => {
          if (!event?.event_tags || !Array.isArray(event.event_tags)) {
            return false;
          }

          // 检查是否包含Creator标签
          return event.event_tags.some((eventTag: any) => {
            const tagSlug = eventTag?.tag?.slug?.toLowerCase();
            return tagSlug === "creator";
          });
        });

        // 更新原始数据缓存
        if (queryParams.offset === 0) {
          setRawData(creatorEvents);
        } else {
          setRawData(prevData => [...prevData, ...creatorEvents]);
        }

        // 应用当前标签过滤
        const currentTagId = queryParams.tag_id?.toString() || "all";

        const filteredData = filterEventsByTag(
          queryParams.offset === 0 ? creatorEvents : [...rawData, ...creatorEvents],
          currentTagId,
        );

        if (queryParams.offset === 0) {
          setData(filteredData);
          setHasMore(true);
        } else {
          setData(filteredData);
        }

        // 基于原始API数据判断是否还有更多，而不是过滤后的数据
        if (allEvents.length < queryParams.limit) {
          setHasMore(false);
        }
        setInitialLoad(false);
      } catch (error) {
        console.error("❌ useInfiniteScrollCreator: Error fetching creator events:", error);
        setHasMore(false);
      } finally {
        setIsLoading(false);
      }
    };

    // 确保有语言信息时才请求数据
    if (current_language) {
      if (queryParams.tag_id === "all") {
        // "all"模式需要等待availableTagIds准备好
        if (availableTagIds !== undefined) {
          fetchDataAsync();
        }
      } else {
        // 非"all"模式可以直接加载
        fetchDataAsync();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [queryParams, current_language]); // 移除availableTagIds依赖，避免影响非all模式的切换

  // 单独处理availableTagIds变化，只在all模式且offset为0时重新加载
  useEffect(() => {
    if (
      current_language &&
      queryParams.tag_id === "all" &&
      availableTagIds !== undefined &&
      queryParams.offset === 0 &&
      !isLoading // 避免在已经加载中时重复触发
    ) {
      // 使用setTimeout避免与其他状态更新冲突
      const timer = setTimeout(() => {
        setQueryParams(prev => ({ ...prev, offset: 0 }));
      }, 50);

      return () => clearTimeout(timer);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [availableTagIds]); // 只依赖availableTagIds

  useEffect(() => {
    if (!observer || !loadMoreRef.current) return;

    observer.observe(loadMoreRef.current);
    return () => observer.disconnect();
  }, [observer]);

  return {
    data,
    hasMore,
    isLoading,
    loadMoreRef,
    setQueryParams,
    setHasMore,
  };
};

export default useInfiniteScrollCreator;
