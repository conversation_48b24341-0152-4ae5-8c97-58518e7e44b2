import React from "react";
import { Button } from "@heroui/react";
import { DollarSign, RefreshCcw } from "lucide-react";
import { useTranslation } from "react-i18next";

interface ClaimFeesButtonProps {
  claimableFees: number;
  isClaimingFees: boolean;
  canClaim: boolean;
  countdown: string;
  clobApis: any;
  onPress: () => void;
}

const ClaimFeesButton: React.FC<ClaimFeesButtonProps> = ({
  claimableFees,
  isClaimingFees,
  canClaim,
  countdown,
  clobApis,
  onPress,
}) => {
  const { t } = useTranslation();

  // 计算按钮是否禁用
  const isDisabled = claimableFees <= 0 || isClaimingFees || !clobApis || !canClaim;

  // 计算按钮样式
  const buttonClassName = `w-full font-semibold text-sm transition-all duration-200 ${
    !isDisabled ? "bg-green-600 text-white hover:bg-green-700" : "bg-gray-300 text-gray-500 cursor-not-allowed"
  }`;

  // 渲染按钮内容
  const renderButtonContent = () => {
    if (isClaimingFees) {
      return (
        <div className="flex items-center gap-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          {t("Wallet_Claiming_Fees")}
        </div>
      );
    }

    if (!canClaim && countdown) {
      return (
        <div className="flex items-center gap-2">
          <RefreshCcw className="size-4" />
          {countdown} 后可领取
        </div>
      );
    }

    if (claimableFees > 0) {
      return (
        <div className="flex items-center gap-2">
          <DollarSign className="size-4" />
          {t("Wallet_Claim_Fees")} ${claimableFees.toFixed(2)}
        </div>
      );
    }

    return t("Wallet_No_Fees");
  };

  return (
    <Button size="lg" variant="solid" onPress={onPress} disabled={isDisabled} className={buttonClassName}>
      {renderButtonContent()}
    </Button>
  );
};

export default ClaimFeesButton;
