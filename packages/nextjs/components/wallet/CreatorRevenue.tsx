"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@heroui/react";
import { ChevronDown, ChevronUp } from "lucide-react";

// Mock 数据
const mockRevenueData = [
  {
    id: 1,
    eventName: "2024年美国总统大选结果预测",
    revenue: "125.50",
    createdAt: "2024-01-15",
  },
  {
    id: 2,
    eventName: "比特币价格是否会在2024年突破10万美元",
    revenue: "89.30",
    createdAt: "2024-01-20",
  },
  {
    id: 3,
    eventName: "OpenAI GPT-5是否会在2024年发布",
    revenue: "67.80",
    createdAt: "2024-02-01",
  },
  {
    id: 4,
    eventName: "特斯拉股价年底是否超过300美元",
    revenue: "45.20",
    createdAt: "2024-02-10",
  },
  {
    id: 5,
    eventName: "苹果公司是否会在2024年发布AR眼镜",
    revenue: "78.90",
    createdAt: "2024-02-15",
  },
  {
    id: 6,
    eventName: "以太坊价格是否会突破5000美元",
    revenue: "56.40",
    createdAt: "2024-02-20",
  },
  {
    id: 7,
    eventName: "ChatGPT用户数是否会超过5亿",
    revenue: "92.10",
    createdAt: "2024-02-25",
  },
  {
    id: 8,
    eventName: "Netflix是否会推出游戏订阅服务",
    revenue: "34.60",
    createdAt: "2024-03-01",
  },
];

const CreatorRevenue: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(false);

  const totalRevenue = mockRevenueData.reduce((sum, item) => sum + parseFloat(item.revenue), 0);

  const handleCreateEvent = () => {
    // TODO: 实现创建事件逻辑
    console.log("Navigate to create event page");
  };

  const handleClaimRevenue = () => {
    // TODO: 实现领取收益逻辑
    console.log("Claim revenue");
  };

  return (
    <div className="w-full flex flex-col border rounded-lg overflow-hidden">
      {/* 收起状态的头部 */}
      <div
        className="flex items-center justify-between p-4 md:p-6 cursor-pointer hover:bg-gray-50 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex flex-col md:flex-row md:items-center">
          <span className="text-base md:text-lg font-semibold text-gray-800">创建者收益分润</span>
          {!isExpanded && (
            <span className="text-xs md:text-sm text-gray-500 md:ml-3 mt-1 md:mt-0">
              总收益: ${totalRevenue.toFixed(2)}
            </span>
          )}
        </div>

        <div className="flex items-center gap-2 md:gap-3">
          {!isExpanded && (
            <div className="flex flex-col md:flex-row gap-1 md:gap-2">
              <div onClick={e => e.stopPropagation()} className="flex flex-col md:flex-row gap-1 md:gap-2">
                <Button
                  size="sm"
                  color="primary"
                  variant="solid"
                  onPress={handleCreateEvent}
                  className="px-2 md:px-4 py-1 md:py-2 text-xs md:text-sm font-medium min-w-16 md:min-w-auto"
                >
                  去创建
                </Button>
                <Button
                  size="sm"
                  variant="solid"
                  onPress={handleClaimRevenue}
                  className="px-2 md:px-4 py-1 md:py-2 text-xs md:text-sm font-medium bg-green-600 hover:bg-green-700 text-white min-w-16 md:min-w-auto"
                >
                  领取收益
                </Button>
              </div>
            </div>
          )}

          <div className="flex items-center justify-center w-5 h-5 md:w-6 md:h-6">
            {isExpanded ? (
              <ChevronUp className="w-4 h-4 md:w-5 md:h-5 text-gray-500" />
            ) : (
              <ChevronDown className="w-4 h-4 md:w-5 md:h-5 text-gray-500" />
            )}
          </div>
        </div>
      </div>

      {/* 展开状态的内容 */}
      {isExpanded && (
        <div className="border-t bg-gray-50">
          {/* 展开状态的操作按钮 */}
          <div className="flex flex-col md:flex-row md:justify-between md:items-center p-4 md:p-6 border-b bg-white gap-3 md:gap-0">
            <div className="flex flex-col md:flex-row md:items-center">
              <span className="text-base md:text-lg font-semibold text-gray-800">收益详情</span>
              <span className="text-xs md:text-sm text-gray-500 md:ml-3 mt-1 md:mt-0">
                总收益: ${totalRevenue.toFixed(2)}
              </span>
            </div>
            <div className="flex gap-2">
              <Button
                size="sm"
                color="primary"
                variant="solid"
                onPress={handleCreateEvent}
                className="px-3 md:px-4 py-2 text-xs md:text-sm font-medium flex-1 md:flex-none"
              >
                去创建
              </Button>
              <Button
                size="sm"
                variant="solid"
                onPress={handleClaimRevenue}
                className="px-3 md:px-4 py-2 text-xs md:text-sm font-medium bg-green-600 hover:bg-green-700 text-white flex-1 md:flex-none"
              >
                领取收益
              </Button>
            </div>
          </div>

          {/* 收益列表 - 添加最大高度和滚动 */}
          <div className="p-4 md:p-6">
            <div className="max-h-80 md:max-h-96 overflow-y-auto scrollbar-hide">
              <div className="space-y-3 md:space-y-4 pb-4">
                {mockRevenueData.map(item => (
                  <div
                    key={item.id}
                    className="flex flex-col md:flex-row md:items-center md:justify-between p-3 md:p-4 bg-white rounded-lg border hover:shadow-sm transition-shadow gap-2 md:gap-0"
                  >
                    <div className="flex-1">
                      <h4 className="text-xs md:text-sm font-medium text-gray-900 mb-1 leading-relaxed">
                        {item.eventName}
                      </h4>
                      <p className="text-xs text-gray-500">创建时间: {item.createdAt}</p>
                    </div>
                    <div className="text-left md:text-right">
                      <div className="text-base md:text-lg font-semibold text-green-600">+${item.revenue}</div>
                      <div className="text-xs text-gray-500">USDC</div>
                    </div>
                  </div>
                ))}
              </div>

              {mockRevenueData.length === 0 && (
                <div className="text-center py-6 md:py-8 text-gray-500">
                  <p className="text-sm">暂无收益记录</p>
                  <p className="text-xs mt-1">创建预测事件开始赚取收益</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CreatorRevenue;
