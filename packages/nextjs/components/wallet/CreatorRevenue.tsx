"use client";

import React, { useState } from "react";
import { Button } from "@heroui/react";
import { ChevronDown, ChevronUp } from "lucide-react";
import { useTranslation } from "react-i18next";

// Mock 数据
const mockRevenueData = [
  {
    id: 1,
    eventName: "2024年美国总统大选结果预测",
    revenue: "125.50",
    createdAt: "2024-01-15",
  },
  {
    id: 2,
    eventName: "比特币价格是否会在2024年突破10万美元",
    revenue: "89.30",
    createdAt: "2024-01-20",
  },
  {
    id: 3,
    eventName: "OpenAI GPT-5是否会在2024年发布",
    revenue: "67.80",
    createdAt: "2024-02-01",
  },
  {
    id: 4,
    eventName: "特斯拉股价年底是否超过300美元",
    revenue: "45.20",
    createdAt: "2024-02-10",
  },
];

const CreatorRevenue: React.FC = () => {
  const { t } = useTranslation();
  const [isExpanded, setIsExpanded] = useState(false);

  const totalRevenue = mockRevenueData.reduce((sum, item) => sum + parseFloat(item.revenue), 0);

  const handleCreateEvent = () => {
    // TODO: 实现创建事件逻辑
    console.log("Navigate to create event page");
  };

  const handleClaimRevenue = () => {
    // TODO: 实现领取收益逻辑
    console.log("Claim revenue");
  };

  return (
    <div className="w-full flex flex-col border rounded-lg overflow-hidden">
      {/* 收起状态的头部 */}
      <div
        className="flex items-center justify-between p-6 cursor-pointer hover:bg-gray-50 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center">
          <span className="text-lg font-semibold text-gray-800">创建者收益分润</span>
          {!isExpanded && <span className="ml-3 text-sm text-gray-500">总收益: ${totalRevenue.toFixed(2)}</span>}
        </div>

        <div className="flex items-center gap-3">
          {!isExpanded && (
            <div className="flex gap-2">
              <Button
                size="sm"
                color="primary"
                variant="solid"
                onClick={e => {
                  e.stopPropagation();
                  handleCreateEvent();
                }}
                className="px-4 py-2 text-sm font-medium"
              >
                去创建
              </Button>
              <Button
                size="sm"
                color="success"
                variant="solid"
                onClick={e => {
                  e.stopPropagation();
                  handleClaimRevenue();
                }}
                className="px-4 py-2 text-sm font-medium"
              >
                领取收益
              </Button>
            </div>
          )}

          <div className="flex items-center justify-center w-6 h-6">
            {isExpanded ? (
              <ChevronUp className="w-5 h-5 text-gray-500" />
            ) : (
              <ChevronDown className="w-5 h-5 text-gray-500" />
            )}
          </div>
        </div>
      </div>

      {/* 展开状态的内容 */}
      {isExpanded && (
        <div className="border-t bg-gray-50">
          {/* 展开状态的操作按钮 */}
          <div className="flex justify-between items-center p-6 border-b bg-white">
            <div className="flex items-center">
              <span className="text-lg font-semibold text-gray-800">收益详情</span>
              <span className="ml-3 text-sm text-gray-500">总收益: ${totalRevenue.toFixed(2)}</span>
            </div>
            <div className="flex gap-2">
              <Button
                size="sm"
                color="primary"
                variant="solid"
                onClick={handleCreateEvent}
                className="px-4 py-2 text-sm font-medium"
              >
                去创建
              </Button>
              <Button
                size="sm"
                color="success"
                variant="solid"
                onClick={handleClaimRevenue}
                className="px-4 py-2 text-sm font-medium"
              >
                领取收益
              </Button>
            </div>
          </div>

          {/* 收益列表 */}
          <div className="p-6">
            <div className="space-y-4">
              {mockRevenueData.map(item => (
                <div
                  key={item.id}
                  className="flex items-center justify-between p-4 bg-white rounded-lg border hover:shadow-sm transition-shadow"
                >
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-gray-900 mb-1">{item.eventName}</h4>
                    <p className="text-xs text-gray-500">创建时间: {item.createdAt}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-semibold text-green-600">+${item.revenue}</div>
                    <div className="text-xs text-gray-500">USDC</div>
                  </div>
                </div>
              ))}
            </div>

            {mockRevenueData.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <p className="text-sm">暂无收益记录</p>
                <p className="text-xs mt-1">创建预测事件开始赚取收益</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default CreatorRevenue;
