import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@heroui/react";
import { Award, DollarSign, RefreshCcw, Shield } from "lucide-react";
import { useTranslation } from "react-i18next";

interface FeesSystemModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const FeesSystemModal: React.FC<FeesSystemModalProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation();

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="lg"
      scrollBehavior="inside"
      classNames={{
        backdrop: "bg-black/50",
        base: "border-[#292f46] bg-white dark:bg-[#19172c] text-gray-900 dark:text-[#a8b0d3]",
        header: "border-b-[1px] border-gray-200 dark:border-[#292f46]",
        body: "py-6",
        closeButton: "hover:bg-gray-100 dark:hover:bg-white/5 active:bg-gray-200 dark:active:bg-white/10",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg">
              <Shield className="size-5 text-white" />
            </div>
            <div className="text-xl font-bold bg-gradient-to-r from-blue-400 to-cyan-500 bg-clip-text text-transparent">
              {t("Wallet_Fees_System_Title")}
            </div>
          </div>
        </ModalHeader>
        <ModalBody>
          <div className="space-y-6">
            <div className="p-4 bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20 rounded-lg border border-amber-200 dark:border-amber-800">
              <div className="text-sm text-amber-800 dark:text-amber-300 leading-relaxed font-medium">
                {t("Wallet_Fees_System_Description")}
              </div>
            </div>

            <div className="grid gap-4">
              <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-800 hover:shadow-md transition-shadow">
                <div className="flex items-center gap-3 mb-3">
                  <div className="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                    <DollarSign className="size-4 text-white" />
                  </div>
                  <div className="font-semibold text-blue-800 dark:text-blue-400 text-base">
                    {t("Wallet_Temporary_Collection")}
                  </div>
                </div>
                <div className="text-sm text-blue-700 dark:text-blue-300 leading-relaxed ml-11">
                  {t("Wallet_Temporary_Collection_Description")}
                </div>
              </div>

              <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg border border-green-200 dark:border-green-800 hover:shadow-md transition-shadow">
                <div className="flex items-center gap-3 mb-3">
                  <div className="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
                    <RefreshCcw className="size-4 text-white" />
                  </div>
                  <div className="font-semibold text-green-800 dark:text-green-400 text-base">
                    {t("Wallet_Full_Refund")}
                  </div>
                </div>
                <div className="text-sm text-green-700 dark:text-green-300 leading-relaxed ml-11">
                  {t("Wallet_Full_Refund_Description")}
                </div>
              </div>

              <div className="p-4 bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-lg border border-purple-200 dark:border-purple-800 hover:shadow-md transition-shadow">
                <div className="flex items-center gap-3 mb-3">
                  <div className="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
                    <Award className="size-4 text-white" />
                  </div>
                  <div className="font-semibold text-purple-800 dark:text-purple-400 text-base">
                    {t("Wallet_Free_For_Real_Users")}
                  </div>
                </div>
                <div className="text-sm text-purple-700 dark:text-purple-300 leading-relaxed ml-11">
                  {t("Wallet_Free_For_Real_Users_Description")}
                </div>
              </div>
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <Button color="primary" variant="solid" onPress={onClose} className="font-medium">
            {t("Normal_Close") || "Close"}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default FeesSystemModal;
