"use client";

import React from "react";
import CustomConnectModal from "@/components/login/CustomConnectModal";
import CountdownTimer from "@/components/redpocket/CountdownTimer";
import { Button, Input } from "@heroui/react";
import { useTranslation } from "react-i18next";

interface InputViewProps {
  password: string;
  isLoading: boolean;
  isLoginModalOpen: boolean;
  onPasswordChange: (value: string) => void;
  onConfirm: () => void;
  onCloseLoginModal: () => void;
  isEventNotStarted?: boolean;
  nextRoundTimestamp?: string;
  onCountdownComplete?: () => void;
}

const InputView: React.FC<InputViewProps> = ({
  password,
  isLoading,
  isLoginModalOpen,
  onPasswordChange,
  onConfirm,
  onCloseLoginModal,
  isEventNotStarted = false,
  nextRoundTimestamp = "",
  onCountdownComplete,
}) => {
  const { t } = useTranslation();

  const isCountdownActive = Boolean(
    isEventNotStarted && nextRoundTimestamp && new Date(nextRoundTimestamp).getTime() > Date.now(),
  );

  return (
    <div className="relative h-[calc(100vh-128px)] flex flex-col items-center justify-center bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50 overflow-hidden">
      {/* 背景装饰元素 - 更丰富的层次 */}
      <div className="absolute inset-0 opacity-25">
        {/* 装饰性圆点 - 更多层次 */}
        <div className="absolute top-20 left-20 w-16 h-16 bg-amber-300 rounded-full blur-2xl animate-pulse"></div>
        <div className="absolute bottom-32 right-16 w-20 h-20 bg-yellow-300 rounded-full blur-2xl animate-pulse delay-1000"></div>
        <div className="absolute top-32 right-32 w-12 h-12 bg-amber-400 rounded-full blur-xl animate-pulse delay-500"></div>
        <div className="absolute bottom-20 left-32 w-14 h-14 bg-orange-300 rounded-full blur-xl animate-pulse delay-1500"></div>

        {/* 浮动装饰 */}
        <div className="absolute top-1/4 left-1/4 w-3 h-3 bg-yellow-400 rounded-full animate-bounce delay-300"></div>
        <div className="absolute top-3/4 right-1/4 w-2 h-2 bg-amber-400 rounded-full animate-bounce delay-700"></div>
        <div className="absolute top-1/3 right-1/3 w-2.5 h-2.5 bg-orange-400 rounded-full animate-bounce delay-1100"></div>
      </div>

      {/* 主要内容 */}
      <div className="relative z-10 flex flex-col items-center justify-center w-full max-w-lg px-6">
        {/* 倒计时显示 - 活动未开始且倒计时未结束时显示 */}
        {isCountdownActive && (
          <div className="mb-6">
            <CountdownTimer
              targetTimestamp={nextRoundTimestamp}
              title={t("redpocket.countdownTitle")}
              onComplete={onCountdownComplete}
            />
          </div>
        )}

        {/* 红包图标 - 更华丽的设计 */}
        <div className="mb-10 relative">
          <div className="w-28 h-28 bg-gradient-to-br from-red-400 via-red-500 to-red-600 rounded-3xl flex items-center justify-center shadow-2xl transform rotate-3 hover:rotate-0 transition-all duration-500 relative overflow-hidden">
            {/* 红包图标 */}
            <div className="relative z-10">
              <svg className="w-14 h-14 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v-.07zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z" />
              </svg>
            </div>

            {/* 装饰圆环 */}
            <div className="absolute inset-2 border-2 border-white/30 rounded-2xl"></div>
          </div>

          {/* 浮动装饰 */}
          <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full animate-bounce"></div>
          <div className="absolute -bottom-1 -left-1 w-5 h-5 bg-amber-400 rounded-full animate-pulse delay-500"></div>
        </div>

        {/* 标题 - 更华丽的设计 */}
        <div className="text-center mb-4">
          <div className="text-2xl font-black text-transparent bg-clip-text bg-gradient-to-r from-amber-700 via-orange-600 to-red-600 mb-4 leading-tight">
            {t("redpocket.title")}
          </div>
        </div>

        {/* 输入区域 - 更精美的设计 */}
        <div className="w-full space-y-8">
          <div className="relative">
            <div className="relative">
              <Input
                placeholder={t("redpocket.inputPlaceholder")}
                value={password}
                onValueChange={onPasswordChange}
                size="md"
                disabled={isLoading}
                className="w-full"
                classNames={{
                  input: "text-center text-xl font-semibold text-gray-700",
                  inputWrapper:
                    "bg-white/90 backdrop-blur-sm border-3 border-amber-200 shadow-2xl hover:border-amber-300 focus-within:border-amber-500 transition-all duration-300 rounded-2xl h-16",
                }}
              />
            </div>
          </div>

          {/* 领取按钮 - 更华丽的设计 */}
          <Button
            onPress={onConfirm}
            size="lg"
            isLoading={isLoading}
            disabled={isLoading || !password.trim() || isCountdownActive}
            className="w-full bg-gradient-to-r from-amber-500 via-orange-500 to-red-500 text-white font-black text-xl py-6 rounded-2xl shadow-2xl hover:shadow-3xl hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 relative overflow-hidden"
          >
            {/* 按钮背景光效 */}
            <div className="absolute inset-0 bg-gradient-to-r from-amber-400 to-orange-400 opacity-0 hover:opacity-20 transition-opacity duration-300"></div>

            <span className="relative z-10 flex items-center justify-center gap-3">
              {isLoading ? (
                <span className="text-lg">{t("redpocket.processing")}</span>
              ) : isCountdownActive ? (
                <span className="text-xl">{t("redpocket.eventNotStarted")}</span>
              ) : (
                <span className="text-xl">{t("redpocket.claimButton")}</span>
              )}
            </span>
          </Button>
        </div>

        {/* 底部提示 - 更精美的设计 */}
        <div className="mt-6 text-center space-y-4">
          <div className="bg-amber-50/80 backdrop-blur-sm border border-amber-200 rounded-2xl px-4 py-2 shadow-lg">
            <div className="flex items-center justify-center gap-1 mb-1">
              <svg className="w-4 h-4 text-amber-600" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                  clipRule="evenodd"
                />
              </svg>
              <span className="text-medium font-semibold text-amber-700">{t("redpocket.securityTip")}</span>
            </div>
            <div className="text-sm text-amber-600 my-2">{t("redpocket.googleLoginTip")}</div>
          </div>
        </div>
      </div>

      {/* 登录弹窗 */}
      <CustomConnectModal isOpen={isLoginModalOpen} onClose={onCloseLoginModal} />
    </div>
  );
};

export default InputView;
