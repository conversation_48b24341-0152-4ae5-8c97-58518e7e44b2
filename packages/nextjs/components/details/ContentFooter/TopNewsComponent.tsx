import React, { useCallback, useEffect, useRef, useState } from "react";
import { getTopNewsListData } from "@/api/events";
import { Image, Link } from "@heroui/react";
import { throttle } from "lodash";

interface TopNewsComponentProps {
  eventTitle: string;
}

const TopNewsComponent: React.FC<TopNewsComponentProps> = ({ eventTitle }) => {
  const [news, setNews] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const observer = useRef<IntersectionObserver | null>(null);

  const fetchNews = useCallback(async () => {
    try {
      setLoading(true);
      const response = await getTopNewsListData(eventTitle, page);
      if (response.data.articles.length === 0) {
        setLoading(false);
        return;
      }
      setNews(prevNews => {
        const newArticles = response.data.articles.filter(
          (article: any) => !prevNews.some((prevArticle: any) => prevArticle.url === article.url),
        );
        return [...prevNews, ...newArticles];
      });
    } catch (err) {
      setError("Failed to fetch news");
    } finally {
      setLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [eventTitle, page]);

  useEffect(() => {
    if (eventTitle) {
      fetchNews();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [eventTitle]);

  const lastNewsElementRef = useCallback(
    throttle((node: any) => {
      if (loading === true) return;
      if (observer.current) observer.current.disconnect();
      observer.current = new IntersectionObserver(entries => {
        if (entries[0].isIntersecting) {
          setPage(prevPage => prevPage + 1);
        }
      });
      if (node) observer.current.observe(node);
    }, 1000),
    [loading],
  );

  if (error) {
    return <div>{error}</div>;
  }

  return (
    <div className="space-y-4">
      {news.length === 0 && !loading && (
        <div className="flex justify-center items-center h-32 bg-gray-100 rounded-lg">
          <div className="text-gray-500">No relevant news</div>
        </div>
      )}

      {news.map((article, index) => (
        <div
          key={index}
          className="flex flex-col md:flex-row items-center w-full hover:bg-gray-100 p-2 rounded-lg"
          ref={index === news.length - 1 ? lastNewsElementRef : null}
        >
          <Image
            src={article.urlToImage}
            alt={article.title}
            radius="lg"
            shadow="lg"
            width={200}
            height={128}
            className="w-full h-auto object-cover rounded-lg"
          />
          <div className="flex flex-col w-full md:w-2/3 ml-0 md:ml-4 mt-4 md:mt-0">
            <Link href={article.url} className="text-blue-500 font-semibold truncate">
              {article.title}
            </Link>
            <div className="line-clamp-3">{article.description}</div>
            <div className="text-gray-500 text-sm mt-1">{new Date(article.publishedAt).toLocaleString()}</div>
          </div>
        </div>
      ))}

      <div className="text-gray-500 text-medium mt-1 flex flex-center">
        {loading === true ? "Loading..." : "No more news"}
      </div>
    </div>
  );
};

export default TopNewsComponent;
