import React, { useEffect, useMemo, useState } from "react";
import { base64DecodeByLanguage } from "@/utils";
import { Divider } from "@heroui/divider";
import { useTranslation } from "react-i18next";
import { useMediaQuery } from "react-responsive";
// import ArtalkComment from "~~/components/details/ContentFooter/ArtComments";
import HotEventsComponent from "~~/components/details/ContentFooter/HotEventsComponent";
import MediaComponent from "~~/components/details/ContentFooter/MediaComponent";
import RelatedEventsComponent from "~~/components/details/ContentFooter/RelatedEventsComponent";
// import CommentsComponent from "~~/components/details/ContentFooter/CommentsComponent";
// import TopNewsComponent from "~~/components/details/ContentFooter/TopNewsComponent";
import { useGlobalState } from "~~/services/store/store";

interface FooterType {
  currentEvent: any;
}
// const tabs = ["Comments", "Top News", "Media"];

const ContentFooter: React.FC<FooterType> = (props: any) => {
  const { currentEvent } = props;
  const { current_language } = useGlobalState().nativeCurrency;
  const { t } = useTranslation();
  // const [eventTitle, setEventTitle] = useState<string>("");
  const [eventMediaUrl, setEventMediaUrl] = useState<string>("");
  const [selectedTab, setSelectedTab] = useState(t("related_events") || "Related Events");

  const isMobile = useMediaQuery({ maxWidth: 767 });

  // 根据设备类型动态生成标签页
  const tabs = useMemo(() => {
    const relatedEventsTab = t("related_events") || "Related Events";
    const hotEventsTab = t("hot_events") || "Hot Events";
    const mediaTab = t("media") || "Media";

    return isMobile
      ? [relatedEventsTab, hotEventsTab, mediaTab] // 移动端显示Hot Events
      : [relatedEventsTab, mediaTab]; // PC端不显示Hot Events
  }, [isMobile, t]);

  // 当设备类型变化时，检查当前选中的标签是否还存在
  useEffect(() => {
    if (!tabs.includes(selectedTab)) {
      setSelectedTab(t("related_events") || "Related Events"); // 重置到默认标签
    }
  }, [isMobile, tabs, selectedTab, t]);

  useEffect(() => {
    // if (currentEvent?.title) {
    //   setEventTitle(base64DecodeByLanguage(currentEvent?.title));
    // }
    if (currentEvent?.multimedia_url) {
      setEventMediaUrl(base64DecodeByLanguage(currentEvent?.multimedia_url));
    }
  }, [current_language, currentEvent?.title, currentEvent?.multimedia_url]);

  return (
    <div className="w-full flex flex-col">
      <Divider className="mb-6" />
      <div className="flex space-x-2">
        {tabs.map(tab => (
          <div
            key={tab}
            className={`cursor-pointer p-2 font-semibold text-xl ${
              selectedTab === tab ? "text-blue-500 border-b-2 border-blue-500" : "text-gray-500"
            }`}
            onClick={() => setSelectedTab(tab)}
          >
            {tab}
          </div>
        ))}
      </div>
      <div className="mt-4">
        {selectedTab === (t("related_events") || "Related Events") && (
          <RelatedEventsComponent currentEvent={currentEvent} />
        )}
        {selectedTab === (t("hot_events") || "Hot Events") && <HotEventsComponent showBorder={false} />}
        {/* {selectedTab === "Comments" && <ArtalkComment pageKey={pageKey} address={address} session={session} />} */}
        {/* {selectedTab === "Top News" && <TopNewsComponent eventTitle={eventTitle} />} */}
        {selectedTab === (t("media") || "Media") && (
          <div>
            <MediaComponent url={eventMediaUrl} />
          </div>
        )}
      </div>
    </div>
  );
};

export default ContentFooter;
